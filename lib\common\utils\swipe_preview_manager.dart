import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';

/// 滑动预览管理器
/// 
/// 负责管理聊天页面的滑动预览效果，包括：
/// - 滑动状态管理
/// - 相邻角色信息管理
/// - 滑动距离计算
/// - 背景位置计算
class SwipePreviewManager extends GetxController {
  // 滑动状态
  final RxDouble _dragOffset = 0.0.obs;
  final RxBool _isDragging = false.obs;
  
  // 相邻角色信息
  final Rxn<AiRole> _previousRole = Rxn<AiRole>();
  final Rxn<AiRole> _nextRole = Rxn<AiRole>();
  
  // 滑动阈值配置
  static const double _switchThreshold = 0.35; // 35%触发切换
  
  // 动画控制器
  AnimationController? _animationController;
  Animation<double>? _animation;
  
  // Getters
  double get dragOffset => _dragOffset.value;
  bool get isDragging => _isDragging.value;
  AiRole? get previousRole => _previousRole.value;
  AiRole? get nextRole => _nextRole.value;
  
  /// 初始化动画控制器
  void initializeAnimation(TickerProvider vsync) {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: vsync,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeOut,
    ));
    
    _animation!.addListener(() {
      _dragOffset.value = _animation!.value;
    });
  }
  
  /// 开始滑动
  void startDrag(double startX) {
    _isDragging.value = true;
    _dragOffset.value = 0.0;
    LogUtil.debug('SwipePreviewManager: 开始滑动');
  }
  
  /// 更新滑动位置
  void updateDrag(double currentX, double startX, double screenWidth) {
    if (!_isDragging.value) return;
    
    final double rawOffset = (currentX - startX) / screenWidth;
    // 限制滑动范围在 -1.0 到 1.0 之间
    _dragOffset.value = rawOffset.clamp(-1.0, 1.0);
  }
  
  /// 结束滑动
  /// 返回是否应该切换角色
  bool endDrag(double screenWidth) {
    if (!_isDragging.value) return false;
    
    _isDragging.value = false;
    final double absOffset = _dragOffset.value.abs();
    
    // 判断是否应该切换
    if (absOffset >= _switchThreshold) {
      LogUtil.debug('SwipePreviewManager: 滑动距离达到切换阈值，执行切换');
      _completeSwitch();
      return true;
    } else {
      LogUtil.debug('SwipePreviewManager: 滑动距离不足，回弹到原位');
      _snapBack();
      return false;
    }
  }
  
  /// 完成切换动画
  void _completeSwitch() {
    if (_animationController == null) return;

    final double targetOffset = _dragOffset.value > 0 ? 1.0 : -1.0;
    _animation = Tween<double>(
      begin: _dragOffset.value,
      end: targetOffset,
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeOut,
    ));

    // 添加监听器来更新dragOffset
    _animation!.addListener(() {
      _dragOffset.value = _animation!.value;
    });

    _animationController!.forward(from: 0.0).then((_) {
      _resetState();
    });
  }
  
  /// 回弹到原位
  void _snapBack() {
    if (_animationController == null) return;

    _animation = Tween<double>(
      begin: _dragOffset.value,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeOut,
    ));

    // 添加监听器来更新dragOffset
    _animation!.addListener(() {
      _dragOffset.value = _animation!.value;
    });

    _animationController!.forward(from: 0.0).then((_) {
      _resetState();
    });
  }
  
  /// 重置状态
  void _resetState() {
    _dragOffset.value = 0.0;
    _isDragging.value = false;
    _animationController?.reset();
  }

  /// 公开的重置滑动状态方法
  /// 用于角色切换完成后重置滑动状态
  void resetSwipeState() {
    _resetState();
    LogUtil.debug('SwipePreviewManager: 已重置滑动状态');
  }

  /// 更新相邻角色信息
  void updateAdjacentRoles(AiRole? previous, AiRole? next) {
    _previousRole.value = previous;
    _nextRole.value = next;
    LogUtil.debug('SwipePreviewManager: 更新相邻角色信息 - 上一个: ${previous?.name}, 下一个: ${next?.name}');
  }
  
  /// 计算背景位置偏移
  /// [isCurrentBackground] 是否为当前背景
  /// [isLeftBackground] 是否为左侧背景（上一个角色）
  double calculateBackgroundOffset(bool isCurrentBackground, bool isLeftBackground) {
    if (!_isDragging.value) {
      return isCurrentBackground ? 0.0 : (isLeftBackground ? -1.0 : 1.0);
    }
    
    if (isCurrentBackground) {
      return _dragOffset.value;
    } else if (isLeftBackground) {
      // 左侧背景：从-1.0位置开始，向右移动
      return -1.0 + _dragOffset.value;
    } else {
      // 右侧背景：从1.0位置开始，向左移动
      return 1.0 + _dragOffset.value;
    }
  }
  
  /// 计算背景透明度
  double calculateBackgroundOpacity(bool isCurrentBackground) {
    final double absOffset = _dragOffset.value.abs();

    if (isCurrentBackground) {
      // 当前背景：滑动时逐渐变透明，从1.0到0.0
      return 1.0 - absOffset;
    } else {
      // 预览背景：滑动时逐渐显示，从0.0到1.0
      return absOffset;
    }
  }
  
  /// 是否应该显示预览
  bool shouldShowPreview() {
    return _dragOffset.value.abs() > 0;
  }
  
  /// 获取当前滑动方向
  SwipeDirection? getCurrentSwipeDirection() {
    if (_dragOffset.value > 0) {
      return SwipeDirection.right; // 向右滑动，显示上一个角色
    } else if (_dragOffset.value < 0) {
      return SwipeDirection.left; // 向左滑动，显示下一个角色
    }
    return null;
  }
  
  @override
  void onClose() {
    _animationController?.dispose();
    super.onClose();
  }
}

/// 滑动方向枚举
enum SwipeDirection {
  left,  // 向左滑动（显示下一个角色）
  right, // 向右滑动（显示上一个角色）
}
