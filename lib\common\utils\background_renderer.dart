import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/swipe_preview_manager.dart';
import 'package:rolio/widgets/cover_image.dart';
import 'package:rolio/common/utils/logger.dart';

/// 背景渲染工具类
/// 
/// 负责渲染聊天页面的多层背景效果，包括：
/// - 当前角色背景
/// - 相邻角色预览背景
/// - 滑动时的位置和透明度计算
class BackgroundRenderer {
  /// 渲染多层背景
  /// 
  /// [currentCoverUrl] 当前角色封面URL
  /// [swipeManager] 滑动预览管理器
  /// [screenSize] 屏幕尺寸
  static Widget renderMultiLayerBackground({
    required String? currentCoverUrl,
    required SwipePreviewManager swipeManager,
    required Size screenSize,
  }) {
    return Stack(
      children: [
        // 当前角色背景
        _buildCurrentBackground(
          coverUrl: currentCoverUrl,
          swipeManager: swipeManager,
          screenSize: screenSize,
        ),
        
        // 上一个角色背景（左侧预览）
        if (swipeManager.previousRole != null)
          _buildPreviewBackground(
            role: swipeManager.previousRole!,
            swipeManager: swipeManager,
            screenSize: screenSize,
            isLeftBackground: true,
          ),
        
        // 下一个角色背景（右侧预览）
        if (swipeManager.nextRole != null)
          _buildPreviewBackground(
            role: swipeManager.nextRole!,
            swipeManager: swipeManager,
            screenSize: screenSize,
            isLeftBackground: false,
          ),
      ],
    );
  }
  
  /// 构建当前角色背景
  static Widget _buildCurrentBackground({
    required String? coverUrl,
    required SwipePreviewManager swipeManager,
    required Size screenSize,
  }) {
    final double offsetX = swipeManager.calculateBackgroundOffset(true, false) * screenSize.width;
    final double opacity = swipeManager.calculateBackgroundOpacity(true);
    
    return Transform.translate(
      offset: Offset(offsetX, 0),
      child: Opacity(
        opacity: opacity,
        child: _buildBackgroundImage(coverUrl, screenSize),
      ),
    );
  }
  
  /// 构建预览背景
  static Widget _buildPreviewBackground({
    required AiRole role,
    required SwipePreviewManager swipeManager,
    required Size screenSize,
    required bool isLeftBackground,
  }) {
    final double offsetX = swipeManager.calculateBackgroundOffset(false, isLeftBackground) * screenSize.width;
    final double opacity = swipeManager.calculateBackgroundOpacity(false);
    
    // 只在应该显示预览时才渲染
    if (!swipeManager.shouldShowPreview()) {
      return const SizedBox.shrink();
    }
    
    // 检查滑动方向是否匹配
    final SwipeDirection? currentDirection = swipeManager.getCurrentSwipeDirection();
    if (currentDirection == null) {
      return const SizedBox.shrink();
    }
    
    // 左侧背景只在向右滑动时显示，右侧背景只在向左滑动时显示
    if ((isLeftBackground && currentDirection != SwipeDirection.right) ||
        (!isLeftBackground && currentDirection != SwipeDirection.left)) {
      return const SizedBox.shrink();
    }
    
    return Transform.translate(
      offset: Offset(offsetX, 0),
      child: Opacity(
        opacity: opacity,
        child: _buildBackgroundImage(role.coverUrl, screenSize),
      ),
    );
  }
  
  /// 构建背景图片
  static Widget _buildBackgroundImage(String? coverUrl, Size screenSize) {
    // 如果URL为空，返回黑色背景
    if (coverUrl == null || coverUrl.isEmpty) {
      return Container(
        width: screenSize.width,
        height: screenSize.height,
        color: Colors.black,
      );
    }

    return Container(
      width: screenSize.width,
      height: screenSize.height,
      alignment: Alignment.center,
      child: IgnorePointer(
        child: ChatCoverImage(
          imageUrl: coverUrl,
          width: screenSize.width,
          height: screenSize.height,
        ),
      ),
    );
  }
  
  /// 预加载相邻角色背景图片
  static void preloadAdjacentBackgrounds(SwipePreviewManager swipeManager) {
    // 预加载上一个角色背景
    if (swipeManager.previousRole != null && 
        swipeManager.previousRole!.coverUrl.isNotEmpty) {
      _preloadImage(swipeManager.previousRole!.coverUrl);
    }
    
    // 预加载下一个角色背景
    if (swipeManager.nextRole != null && 
        swipeManager.nextRole!.coverUrl.isNotEmpty) {
      _preloadImage(swipeManager.nextRole!.coverUrl);
    }
  }
  
  /// 预加载单个图片
  static void _preloadImage(String imageUrl) {
    try {
      // 使用Flutter的预加载机制
      precacheImage(NetworkImage(imageUrl), Get.context!);
      LogUtil.debug('BackgroundRenderer: 预加载背景图片 - $imageUrl');
    } catch (e) {
      LogUtil.error('BackgroundRenderer: 预加载背景图片失败 - $imageUrl, 错误: $e');
    }
  }
  
  /// 清理缓存的背景图片
  static void clearBackgroundCache() {
    try {
      // 清理图片缓存
      PaintingBinding.instance.imageCache.clear();
      LogUtil.debug('BackgroundRenderer: 已清理背景图片缓存');
    } catch (e) {
      LogUtil.error('BackgroundRenderer: 清理背景图片缓存失败 - $e');
    }
  }
}
